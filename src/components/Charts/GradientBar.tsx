import '@/utils/theme';
import { Bar, BarConfig } from '@ant-design/charts';

export type BarProps = BarConfig;
const GradientBar: React.FC<BarProps> = (props) => {
  const config: BarConfig = {
    legend: {
      position: 'top-left',
    },
    minBarWidth: 18,
    maxBarWidth: 18,
    autoFit: true,

    xAxis: {
      max: 1, // 固定最大值 100%
      grid: null,
      line: null,
      tickLine: null,
      label: null,
    },
    yAxis: {
      line: null,
      tickLine: null,
      label: {
        offset: 24,
      },
    },
    barStyle: {
      fill: 'l(0) 0:rgba(58,171,249,0.8) 1:rgba(90,242,219,0.8)',
      radius: [10, 10, 10, 10],
    },
    barBackground: {
      style: {
        fill: 'rgba(235, 235, 235.8)',
        radius: [10, 10, 10, 10],
      },
    },
    ...props,
  };

  return <Bar {...config} />;
};

export default GradientBar;
