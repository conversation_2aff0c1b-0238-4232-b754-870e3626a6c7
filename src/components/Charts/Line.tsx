import '@/utils/theme';
import { Line, LineConfig } from '@ant-design/charts';

export type LineProps = Partial<LineConfig> & {
  data: LineConfig['data'];
};
const RkLine: React.FC<LineProps> = ({ data = [], ...rest }) => {
  const config: LineConfig = {
    data,
    padding: 'auto',
    yAxis: {
      grid: {
        line: { style: { stroke: '#F5F5F5' } },
      },
      line: null,
      tickLine: null,
    },
    ...rest,
  };

  return <Line {...config} />;
};

export default RkLine;
