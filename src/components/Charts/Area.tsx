import '@/utils/theme';
import { Area, AreaConfig } from '@ant-design/charts';

export type AreaProps = Partial<AreaConfig> & {
  data: AreaConfig['data'];
};
const RkArea: React.FC<AreaProps> = ({ data, ...rest }) => {
  const enableScrollbar = data.length > 12;
  const config: AreaConfig = {
    data,
    xField: 'time',
    yField: 'value',
    xAxis: {
      range: [0, 1],
      tickCount: 12,
      label: {
        autoHide: true,
        autoRotate: false,
        style: { fill: '#1A1A1A' },
      },
      line: {
        style: { stroke: '#1A1A1A', lineWidth: 1 },
      },
      tickLine: {
        style: { stroke: '#1A1A1A' },
      },
    },
    yAxis: {
      grid: {
        line: { style: { stroke: '#F5F5F5' } },
      },
      line: null,
      tickLine: null,
      label: {
        style: { fill: '#1A1A1A' },
      },
    },
    point: {
      size: 5,
      shape: 'dot',
      style: {
        fill: 'white',
        lineWidth: 2,
      },
    },
    areaStyle: () => ({ fillOpacity: 0.2 }),
    scrollbar: enableScrollbar ? { type: 'horizontal' } : undefined,
    slider: enableScrollbar ? { start: 0.5, end: 1 } : undefined,
    ...rest,
  };

  return <Area {...config} />;
};

export default RkArea;
