import '@/utils/theme';
import { G2, Gauge, GaugeConfig } from '@ant-design/charts';
import React, { useMemo } from 'react';
import img from './bg.svg';
import styles from './index.less';
import { getGradientColor } from './utils';
export type GaugeProps = Partial<GaugeConfig> & {
  percent: number;
  title: string;
  info?: string;
  titleOffsetY?: number;
  contentOffsetY?: number;
};

// 注册自定义形状（只注册一次）
let isShapeRegistered = false;

const registerTriangleGaugeIndicator = () => {
  if (isShapeRegistered) return;

  const { registerShape, Util } = G2;
  registerShape('point', 'triangle-gauge-indicator', {
    draw(cfg, container) {
      const { indicator } = cfg.customInfo;
      const { pointer } = indicator;
      const group = container.addGroup(); // 获取极坐标系下画布中心点
      // @ts-ignore
      const center = this.parsePoint({
        x: 0,
        y: 0,
      }); // 绘制指针

      const baseRatio = 0.75; // 越大，越靠近圆环 0.75
      const headRatio = 0.85; // 越大，指针更长 0.85
      const angleOffset = Math.PI / 40; // 越小越尖 40

      if (pointer) {
        // @ts-ignore
        const { startAngle, endAngle } = Util.getAngle(cfg, this.coordinate);
        // @ts-ignore
        const radius = this.coordinate.getRadius();
        const midAngle = (startAngle + endAngle) / 2;
        const { x: x1, y: y1 } = Util.polarToCartesian(
          center.x,
          center.y,
          radius * baseRatio,
          midAngle + angleOffset,
        );
        const { x: x2, y: y2 } = Util.polarToCartesian(
          center.x,
          center.y,
          radius * baseRatio,
          midAngle - angleOffset,
        );
        const { x, y } = Util.polarToCartesian(center.x, center.y, radius * headRatio, midAngle);
        const path = [['M', x1, y1], ['L', x, y], ['L', x2, y2], ['Z']]; // pointer

        group.addShape('path', {
          name: 'pointer',
          attrs: {
            path,
            ...pointer.style,
          },
        });
      }

      return group;
    },
  });

  isShapeRegistered = true;
};

// 在模块加载时立即注册自定义形状
registerTriangleGaugeIndicator();

const RkGauge: React.FC<GaugeProps> = ({
  percent,
  title,
  radius = 1,
  autoFit,
  width = 400,
  height = 200,
  info = '',
  titleOffsetY = -20,
  contentOffsetY = 0,

  ...rest
}) => {
  // 确保在渲染前注册自定义形状
  registerTriangleGaugeIndicator();

  const config: GaugeConfig = useMemo(
    () => ({
      percent,
      radius,
      autoFit: autoFit !== false, // 默认为true，除非明确设置为false
      startAngle: Math.PI,
      endAngle: 2 * Math.PI,
      // 设置尺寸
      width,
      height,
      range: {
        ticks: [0, 0.5, 1],
        color: ['l(0) 0:#37c8c7 1:#47b7ae', 'l(0) 0:#47b7ae 0.5:#a6977d  1:#f27c55'],
      },
      indicator: {
        shape: 'triangle-gauge-indicator',
        pointer: {
          style: {
            fill: getGradientColor(percent),
          },
        },
      },
      axis: false,
      gaugeStyle: {
        lineCap: 'round',
      },
      statistic: {
        title: {
          offsetY: titleOffsetY,
          style: {
            fontSize: '16px',
            color: '#101010',
          },
          formatter: (datum) => {
            const value = datum?.percent * 100;
            return value % 1 === 0 ? `${value}%` : `${value.toFixed(1)}%`;
          },
        },
        content: {
          offsetY: contentOffsetY,
          style: {
            fontSize: '14px',
            lineHeight: '14px',
            color: '#101010',
          },
          customHtml: () => {
            return (
              <>
                <div className={styles.title}>{title}</div>
                <span className={styles.info}>{info}</span>
              </>
            );
          },
        },
      },
      ...rest,
    }),
    [percent, title, rest],
  );

  return (
    <div className={styles.gauge}>
      <img src={img} className={styles.img} />
      <Gauge {...config} />
    </div>
  );
};

export default RkGauge;
