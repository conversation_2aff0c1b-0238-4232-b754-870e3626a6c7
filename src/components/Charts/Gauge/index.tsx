import '@/utils/theme';
import { G2, Gauge, GaugeConfig } from '@ant-design/charts';
import React, { useMemo } from 'react';
import { getGradientColor } from './utils';

export type GaugeProps = Partial<GaugeConfig> & {
  percent: number;
  title: string;
};

// 注册自定义形状（只注册一次）
let isShapeRegistered = false;

const registerTriangleGaugeIndicator = () => {
  if (isShapeRegistered) return;

  const { registerShape, Util } = G2;
  registerShape('point', 'triangle-gauge-indicator', {
    draw(cfg, container) {
      const { indicator } = cfg.customInfo;
      const { pointer } = indicator;
      const group = container.addGroup(); // 获取极坐标系下画布中心点
      // @ts-ignore
      const center = this.parsePoint({
        x: 0,
        y: 0,
      }); // 绘制指针

      const baseRatio = 0.75; // 越大，越靠近圆环
      const headRatio = 0.85; // 越大，指针更长
      const angleOffset = Math.PI / 40; // 越小越尖

      if (pointer) {
        // @ts-ignore
        const { startAngle, endAngle } = Util.getAngle(cfg, this.coordinate);
        // @ts-ignore
        const radius = this.coordinate.getRadius();
        const midAngle = (startAngle + endAngle) / 2;
        const { x: x1, y: y1 } = Util.polarToCartesian(
          center.x,
          center.y,
          radius * baseRatio,
          midAngle + angleOffset,
        );
        const { x: x2, y: y2 } = Util.polarToCartesian(
          center.x,
          center.y,
          radius * baseRatio,
          midAngle - angleOffset,
        );
        const { x, y } = Util.polarToCartesian(center.x, center.y, radius * headRatio, midAngle);
        const path = [['M', x1, y1], ['L', x, y], ['L', x2, y2], ['Z']]; // pointer

        group.addShape('path', {
          name: 'pointer',
          attrs: {
            path,
            ...pointer.style,
          },
        });
      }

      return group;
    },
  });

  isShapeRegistered = true;
};

// 在模块加载时立即注册自定义形状
registerTriangleGaugeIndicator();

const RkGauge: React.FC<GaugeProps> = ({ percent, title, ...rest }) => {
  // 确保在渲染前注册自定义形状
  registerTriangleGaugeIndicator();

  // 使用 useMemo 优化配置对象，避免不必要的重新创建
  const config: GaugeConfig = useMemo(
    () => ({
      percent,
      radius: 0.8,
      autoFit: true,
      startAngle: Math.PI,
      endAngle: 2 * Math.PI,
      range: {
        ticks: [0, 0.5, 1],
        color: ['l(0) 0:#37c8c7 1:#47b7ae', 'l(0) 0:#47b7ae 0.5:#a6977d  1:#f27c55'],
      },
      indicator: {
        shape: 'triangle-gauge-indicator',
        pointer: {
          style: {
            fill: getGradientColor(percent),
          },
        },
      },
      axis: false,
      gaugeStyle: {
        lineCap: 'round',
      },
      statistic: {
        title: {
          offsetY: -20,
          style: {
            fontSize: '16px',
            color: '#4B535E',
          },
          formatter: () => title,
        },
        content: {
          style: {
            fontSize: '14px',
            lineHeight: '26px',
            color: '#4B535E',
          },
          formatter: (datum) => {
            const value = datum?.percent * 100;
            return value % 1 === 0 ? `${value}%` : `${value.toFixed(1)}%`;
          },
        },
      },
      ...rest,
    }),
    [percent, title, rest],
  );

  return <Gauge {...config} />;
};

export default RkGauge;
