declare namespace API {
  type alarmFindByIdParams = {
    id: string;
  };

  type AlarmHandlingSuggestion = {
    suggestion?: string;
    time?: string;
    generated?: 'DeepSeek' | 'Manual';
  };

  type AlarmIdsRequest = {
    /** ids */
    ids?: string[];
  };

  type AlarmVO = {
    id?: string;
    suggestion?: AlarmHandlingSuggestion;
  };

  type AtomicTaskPathRequest = {
    path?: string;
  };

  type AtomicTaskPathVO = {
    name?: string;
    path?: string;
    content?: string;
    isDirectory?: boolean;
    isAtomicTask?: boolean;
    children?: AtomicTaskPathVO[];
  };

  type AtomicTaskUploadPageRequest = {
    page?: PageRequest;
  };

  type AtomicTaskUploadVO = {
    id?: string;
    fileName?: string;
    status?: number;
    uploadTime?: string;
    endTime?: string;
  };

  type AtomicTaskVO = {
    id?: string;
    name?: string;
    path?: string;
  };

  type Auth = {
    id?: string;
    permission?: number;
  };

  type BatchIdRequest = {
    ids?: string[];
  };

  type CommonRequest = {
    method?: string;
    params?: JsonNode;
  };

  type CUPanoramicGroupRequest = {
    name?: string;
    hosts?: PanoramicObjectCreateRequest[];
    top?: boolean;
    sort?: number;
  };

  type DeviceStatistics = {
    group?: string;
    count?: string;
  };

  type externalMonitoringPanelByIdParams = {
    id: string;
  };

  type ExternalMonitoringPanelCURequest = {
    name?: string;
    uri?: string;
    description?: string;
  };

  type ExternalMonitoringPanelPageRequest = {
    /** 筛选 */
    filter?: Record<string, any>;
    /** 搜索 */
    search?: Record<string, any>;
    page?: PageRequest;
  };

  type ExternalMonitoringPanelPageVO = {
    id?: string;
    name?: string;
    uri?: string;
    description?: string;
  };

  type externalMonitoringPanelUpdateParams = {
    id: string;
  };

  type ExternalMonitoringPanelVO = {
    id?: string;
    name?: string;
    uri?: string;
    description?: string;
  };

  type getTopologyParams = {
    id: string;
  };

  type GlobalConfigRequest = {
    /** 全局刷新秒数 */
    refreshSeconds?: number;
  };

  type GlobalConfigVO = {
    /** 全局刷新秒数 */
    refreshSeconds?: number;
  };

  type Healthy = {
    count?: number;
    warning?: number;
    normal?: number;
  };

  type HostAgentCURequest = {
    /** ip类型 */
    ipType?: 'Server' | 'Proxy';
    /** IP */
    ip?: string;
    /** Agent端口 */
    port?: string;
    /** 安装路径 */
    path?: string;
  };

  type HostAgentPageVO = {
    /** agent安装状态 */
    status?: 'Success' | 'Running' | 'Fail';
    /** agent版本 */
    version?: string;
  };

  type HostAgentVO = {
    /** agent安装状态 */
    status?: 'Success' | 'Running' | 'Fail';
    /** agent版本 */
    version?: string;
    /** agentId */
    agentId?: string;
    /** ip类型 */
    ipType?: 'Server' | 'Proxy';
    /** IP */
    ip?: string;
    /** Agent端口 */
    port?: string;
    /** 安装路径 */
    path?: string;
  };

  type HostBatchTestConnectionVO = {
    /** id */
    id?: string;
    /** 可用状态(0: 不可用,1: 可用,-1: 未知) */
    usability?: '0' | '1' | '-1';
  };

  type HostCURequest = {
    /** 类型 */
    type?: 'Linux' | 'Windows' | 'Unix';
    /** 主机 */
    host?: string;
    /** 端口 */
    port?: number;
    /** 认证方式 */
    authenticationType?: 'PASSWORD' | 'SSH_KEY';
    /** 部署密钥方式 */
    deployKeyType?: 'AUTO' | 'MANUAL';
    /** 密钥id */
    sshKeyId?: string;
    /** 用户名 */
    username?: string;
    /** 密码 */
    password?: string;
    /** 描述 */
    description?: string;
    agent?: HostAgentCURequest;
  };

  type HostDeployAgentRequest = {
    id?: string;
  };

  type HostDeployKeyRequest = {
    /** 类型 */
    type?: 'Linux' | 'Windows' | 'Unix';
    /** 主机 */
    host?: string;
    /** 端口 */
    port?: number;
    /** 认证方式 */
    authenticationType?: 'PASSWORD' | 'SSH_KEY';
    /** 部署密钥方式 */
    deployKeyType?: 'AUTO' | 'MANUAL';
    /** 密钥id */
    sshKeyId?: string;
    /** 用户名 */
    username?: string;
    /** 密码 */
    password?: string;
    /** 描述 */
    description?: string;
    agent?: HostAgentCURequest;
    id?: string;
  };

  type hostFindByIdParams = {
    id: string;
  };

  type HostGroup = {
    groupid?: string;
    name?: string;
    flags?: number;
    internal?: number;
    uuid?: string;
  };

  type HostGroupCURequest = {
    /** 名称 */
    name?: string;
    /** 主机ids */
    hostIds?: string[];
    /** 描述 */
    description?: string;
  };

  type HostGroupFindByIdParams = {
    id: string;
  };

  type HostGroupListVO = {
    /** id */
    id?: string;
    /** 名称 */
    name?: string;
    /** 描述 */
    description?: string;
  };

  type HostGroupPageRequest = {
    /** 筛选 */
    filter?: Record<string, any>;
    /** 搜索 */
    search?: Record<string, any>;
    page?: PageRequest;
  };

  type HostGroupPageVO = {
    /** id */
    id?: string;
    /** 名称 */
    name?: string;
    /** 描述 */
    description?: string;
  };

  type HostGroupUpdateByIdParams = {
    id: string;
  };

  type HostGroupVO = {
    /** id */
    id?: string;
    /** 名称 */
    name?: string;
    /** 主机 */
    hosts?: HostVO[];
    /** 描述 */
    description?: string;
  };

  type HostMacro = {
    hostmacroid?: string;
    hostid?: string;
    macro?: string;
    value?: string;
    type?: number;
    description?: string;
  };

  type HostPageRequest = {
    /** 主机组ids */
    groupIds?: string[];
    /** 筛选 */
    filter?: Record<string, any>;
    /** 搜索 */
    search?: Record<string, any>;
    page?: PageRequest;
  };

  type HostPageVO = {
    /** id */
    id?: string;
    /** 可用状态(0: 不可用,1: 可用,-1: 未知) */
    usability?: '0' | '1' | '-1';
    /** 类型 */
    type?: 'Linux' | 'Windows' | 'Unix';
    /** 主机 */
    host?: string;
    /** 端口 */
    port?: number;
    /** 用户名 */
    username?: string;
    /** 描述 */
    description?: string;
    agent?: HostAgentPageVO;
  };

  type HostSshKeyCURequest = {
    /** 名称 */
    name?: string;
    /** 公钥 */
    publicKey?: string;
    /** 私钥 */
    privateKey?: string;
    /** 类型 */
    keyType?: 'RSA' | 'DSA' | 'ECDSA' | 'ED25519';
    /** 描述 */
    description?: string;
  };

  type hostSshKeyFindByIdParams = {
    id: string;
  };

  type HostSshKeyPageRequest = {
    /** 筛选 */
    filter?: Record<string, any>;
    /** 搜索 */
    search?: Record<string, any>;
    page?: PageRequest;
  };

  type HostSshKeyPageVO = {
    /** id */
    id?: string;
    /** 名称 */
    name?: string;
    /** 类型 */
    keyType?: 'RSA' | 'DSA' | 'ECDSA' | 'ED25519';
    /** 描述 */
    description?: string;
  };

  type hostSshKeyUpdateByIdParams = {
    id: string;
  };

  type HostSshKeyVO = {
    /** id */
    id?: string;
    /** 名称 */
    name?: string;
    /** 公钥 */
    publicKey?: string;
    /** 私钥 */
    privateKey?: string;
    /** 类型 */
    keyType?: 'RSA' | 'DSA' | 'ECDSA' | 'ED25519';
    /** 描述 */
    description?: string;
  };

  type HostTestConnectionRequest = {
    /** 类型 */
    type?: 'Linux' | 'Windows' | 'Unix';
    /** 主机 */
    host?: string;
    /** 端口 */
    port?: number;
    /** 认证方式 */
    authenticationType?: 'PASSWORD' | 'SSH_KEY';
    /** 部署密钥方式 */
    deployKeyType?: 'AUTO' | 'MANUAL';
    /** 密钥id */
    sshKeyId?: string;
    /** 用户名 */
    username?: string;
    /** 密码 */
    password?: string;
    /** 描述 */
    description?: string;
    agent?: HostAgentCURequest;
    id?: string;
  };

  type hostUpdateByIdParams = {
    id: string;
  };

  type HostVO = {
    /** id */
    id?: string;
    /** 可用状态(0: 不可用,1: 可用,-1: 未知) */
    usability?: '0' | '1' | '-1';
    /** 类型 */
    type?: 'Linux' | 'Windows' | 'Unix';
    /** 主机 */
    host?: string;
    /** 端口 */
    port?: number;
    /** 认证方式 */
    authenticationType?: 'PASSWORD' | 'SSH_KEY';
    /** 部署密钥方式 */
    deployKeyType?: 'AUTO' | 'MANUAL';
    sshKey?: HostSshKeyVO;
    /** 用户名 */
    username?: string;
    /** 描述 */
    description?: string;
    agent?: HostAgentVO;
  };

  type IdVO = {
    id?: string;
  };

  type JsonNode = true;

  type LoginRequest = {
    account?: string;
    password?: string;
  };

  type LoginVO = {
    user?: UserVO;
    token?: string;
  };

  type Media = {
    /** 被使用的媒介类型ID. */
    mediatypeid?: string;
    /** 地址, 用户名或者其他接收标识符.
如果 媒介类型 是邮件, 值被定义为数组. 如果 媒介类型 是其他类型, 值被定义为字符串.
 */
    sendto?: string[];
    /** 是否启用媒介.
可用值:
0 - (default) 启用;
1 - 禁用.
 */
    active?: number;
    /** 触发媒介发送告警的告警级别.
每一位数字代表一个告警级别，并以二进制形式存储. 例如, 12 相当于二进制的 1100, 它表示告警级别为警告和一般严重的告警将触发告警媒介.
参阅 触发器对象 查看告警级别列表.
默认: 63
 */
    severity?: number;
    /** 时间窗口: 能够发送告警通知的 时间段 或者以分号分隔的用户宏.
默认: 1-7,00:00-24:00
 */
    period?: string;
  };

  type MediasItem = {
    severity?: string;
    period?: string;
    sendto?: string[];
    mediatypeid?: string;
    active?: string;
    mediaid?: string;
    userid?: string;
  };

  type MediatypesItem = {
    smtp_authentication?: string;
    mediatypeid?: string;
    maxsessions?: string;
    smtp_verify_host?: string;
    description?: string;
    maxattempts?: string;
    type?: string;
    timeout?: string;
    exec_path?: string;
    content_type?: string;
    event_menu_url?: string;
    exec_params?: string;
    gsm_modem?: string;
    script?: string;
    smtp_security?: string;
    smtp_verify_peer?: string;
    smtp_email?: string;
    smtp_helo?: string;
    show_event_menu?: string;
    smtp_port?: string;
    smtp_server?: string;
    passwd?: string;
    attempt_interval?: string;
    event_menu_name?: string;
    name?: string;
    process_tags?: string;
    parameters?: Record<string, any>[];
    username?: string;
    status?: string;
  };

  type MonitoringIconRequest = {
    name?: string;
    icon?: string;
  };

  type MonitoringIconVO = {
    name?: string;
    icon?: string;
    isExtends?: boolean;
  };

  type MonitoringObjectGroupVO = {
    groupId?: string;
    name?: string;
    hostData?: MonitoringObjectVO[];
    icon?: MonitoringIconVO;
  };

  type MonitoringObjectTagVO = {
    tag?: string;
    value?: string;
  };

  type MonitoringObjectVO = {
    hostId?: string;
    name?: string;
    status?: string;
    icon?: MonitoringIconVO;
    inheritedTags?: MonitoringObjectTagVO[];
  };

  type MonitorObjectCreateRequest = {
    params?: Record<string, any>;
  };

  type MonitorObjectMacroVO = {
    /** id */
    id?: string;
    /** 名称 */
    name?: string;
    /** 宏名 */
    macro?: string;
  };

  type MonitorObjectSubtypeVO = {
    /** id */
    id?: string;
    /** 名称 */
    name?: string;
    /** 类型 */
    type?: string;
    /** 接口类型 */
    interfaceType?: string;
    /** 宏 */
    macros?: MonitorObjectMacroVO[];
  };

  type MonitorObjectTypeVO = {
    /** id */
    id?: string;
    /** 名称 */
    name?: string;
    /** 类型 */
    type?: string;
    /** 子类型 */
    subTypes?: MonitorObjectSubtypeVO[];
  };

  type Operator = {
    id?: string;
    name?: string;
  };

  type PageRequest = {
    /** 数量 */
    size?: number;
    /** 页码 */
    page?: number;
  };

  type PageVOAtomicTaskUploadVO = {
    /** 数据 */
    data?: AtomicTaskUploadVO[];
    /** 页码 */
    page?: number;
    /** 数量 */
    size?: number;
    /** 总数 */
    total?: string;
    /** 总页 */
    pageTotal?: number;
  };

  type PageVOExternalMonitoringPanelPageVO = {
    /** 数据 */
    data?: ExternalMonitoringPanelPageVO[];
    /** 页码 */
    page?: number;
    /** 数量 */
    size?: number;
    /** 总数 */
    total?: string;
    /** 总页 */
    pageTotal?: number;
  };

  type PageVOHostGroupPageVO = {
    /** 数据 */
    data?: HostGroupPageVO[];
    /** 页码 */
    page?: number;
    /** 数量 */
    size?: number;
    /** 总数 */
    total?: string;
    /** 总页 */
    pageTotal?: number;
  };

  type PageVOHostPageVO = {
    /** 数据 */
    data?: HostPageVO[];
    /** 页码 */
    page?: number;
    /** 数量 */
    size?: number;
    /** 总数 */
    total?: string;
    /** 总页 */
    pageTotal?: number;
  };

  type PageVOHostSshKeyPageVO = {
    /** 数据 */
    data?: HostSshKeyPageVO[];
    /** 页码 */
    page?: number;
    /** 数量 */
    size?: number;
    /** 总数 */
    total?: string;
    /** 总页 */
    pageTotal?: number;
  };

  type PageVOPanoramicGroupPageVO = {
    /** 数据 */
    data?: PanoramicGroupPageVO[];
    /** 页码 */
    page?: number;
    /** 数量 */
    size?: number;
    /** 总数 */
    total?: string;
    /** 总页 */
    pageTotal?: number;
  };

  type PageVOPanoramicGroupProblemPageVO = {
    /** 数据 */
    data?: PanoramicGroupProblemPageVO[];
    /** 页码 */
    page?: number;
    /** 数量 */
    size?: number;
    /** 总数 */
    total?: string;
    /** 总页 */
    pageTotal?: number;
  };

  type PageVOTaskModuleOrchestrationExecutePageVO = {
    /** 数据 */
    data?: TaskModuleOrchestrationExecutePageVO[];
    /** 页码 */
    page?: number;
    /** 数量 */
    size?: number;
    /** 总数 */
    total?: string;
    /** 总页 */
    pageTotal?: number;
  };

  type PageVOTaskModuleOrchestrationPageVO = {
    /** 数据 */
    data?: TaskModuleOrchestrationPageVO[];
    /** 页码 */
    page?: number;
    /** 数量 */
    size?: number;
    /** 总数 */
    total?: string;
    /** 总页 */
    pageTotal?: number;
  };

  type PageVOTaskModulePageVO = {
    /** 数据 */
    data?: TaskModulePageVO[];
    /** 页码 */
    page?: number;
    /** 数量 */
    size?: number;
    /** 总数 */
    total?: string;
    /** 总页 */
    pageTotal?: number;
  };

  type PageVOTopologyPageVO = {
    /** 数据 */
    data?: TopologyPageVO[];
    /** 页码 */
    page?: number;
    /** 数量 */
    size?: number;
    /** 总数 */
    total?: string;
    /** 总页 */
    pageTotal?: number;
  };

  type PanoramicGroupPageRequest = {
    /** 筛选 */
    filter?: Record<string, any>;
    /** 搜索 */
    search?: Record<string, any>;
    page?: PageRequest;
  };

  type PanoramicGroupPageVO = {
    id?: string;
    name?: string;
    hostIds?: string[];
    top?: boolean;
    hosts?: PanoramicObjectPageVO[];
    sort?: number;
  };

  type PanoramicGroupProblemPageVO = {
    id?: string;
    name?: string;
    hostIds?: string[];
    top?: boolean;
    hosts?: PanoramicObjectPageVO[];
    sort?: number;
    health?: number;
    problemList?: ProblemVO[];
  };

  type PanoramicObjectCreateRequest = {
    hostId?: string;
    name?: string;
    sort?: number;
  };

  type PanoramicObjectPageVO = {
    hostid?: string;
    name?: string;
    sort?: number;
  };

  type ProblemVO = {
    eventId?: string;
    severity?: string;
  };

  type ResultAlarmVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: AlarmVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultAtomicTaskPathVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: AtomicTaskPathVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultBoolean = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: boolean;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultExternalMonitoringPanelVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: ExternalMonitoringPanelVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultGlobalConfigVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: GlobalConfigVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultHostGroupVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: HostGroupVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultHostSshKeyVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: HostSshKeyVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultHostVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: HostVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIdVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IdVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultInteger = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: number;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultJsonNode = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: JsonNode;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListAlarmVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: AlarmVO[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListAtomicTaskVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: AtomicTaskVO[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListHostBatchTestConnectionVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: HostBatchTestConnectionVO[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListHostGroupListVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: HostGroupListVO[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListMonitoringObjectGroupVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: MonitoringObjectGroupVO[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListMonitorObjectTypeVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: MonitorObjectTypeVO[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListTaskModuleChangeHistoryVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: TaskModuleChangeHistoryVO[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListTemplateGetDTO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: TemplateGetDTO[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListUserGroupDTO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: UserGroupDTO[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListUserInfoResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: UserInfoResponse[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultLoginVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: LoginVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultObject = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: Record<string, any>;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultPageVOAtomicTaskUploadVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageVOAtomicTaskUploadVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultPageVOExternalMonitoringPanelPageVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageVOExternalMonitoringPanelPageVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultPageVOHostGroupPageVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageVOHostGroupPageVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultPageVOHostPageVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageVOHostPageVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultPageVOHostSshKeyPageVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageVOHostSshKeyPageVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultPageVOPanoramicGroupPageVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageVOPanoramicGroupPageVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultPageVOPanoramicGroupProblemPageVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageVOPanoramicGroupProblemPageVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultPageVOTaskModuleOrchestrationExecutePageVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageVOTaskModuleOrchestrationExecutePageVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultPageVOTaskModuleOrchestrationPageVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageVOTaskModuleOrchestrationPageVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultPageVOTaskModulePageVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageVOTaskModulePageVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultPageVOTopologyPageVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageVOTopologyPageVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultStatisticsVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: StatisticsVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultString = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: string;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultTaskModuleOrchestrationExecuteVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: TaskModuleOrchestrationExecuteVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultTaskModuleOrchestrationVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: TaskModuleOrchestrationVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultTaskModuleVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: TaskModuleVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultTemplateIdDTO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: TemplateIdDTO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultTopologyVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: TopologyVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultUserAuthenticationResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: UserAuthenticationResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultUserGroupIdDTO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: UserGroupIdDTO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultUserIdsResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: UserIdsResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultUserVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: UserVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type Role = {
    readonly?: string;
    roleid?: string;
    name?: string;
    type?: string;
  };

  type StatisticsVO = {
    healthy?: Healthy;
    todayWarning?: TodayWarning;
    warningCount?: WarningCount;
    warningTops?: WarningTop[];
    deviceStatistics?: DeviceStatistics[];
  };

  type TagAuth = {
    groupid?: string;
    tag?: string;
    value?: string;
  };

  type taskModuleChangeHistoryParams = {
    id: string;
  };

  type TaskModuleChangeHistoryVO = {
    id?: string;
    taskModuleId?: string;
    changeTime?: string;
    operatorId?: string;
    operatorName?: string;
  };

  type TaskModuleCURequest = {
    name?: string;
    preInspectionTaskId?: string;
    paramsMergeType?: string;
    params?: string;
    taskModuleFront?: JsonNode;
    description?: string;
  };

  type TaskModuleExecutePageVO = {
    id?: string;
    taskModule?: TaskModulePageVO;
    status?: string;
  };

  type TaskModuleExecuteVO = {
    id?: string;
    taskModule?: TaskModuleVO;
    preInspectionTaskResult?: TaskResultVO;
    taskResult?: TaskResultVO[];
    status?: string;
  };

  type taskModuleFindByIdParams = {
    id: string;
  };

  type TaskModuleOrchestrationCURequest = {
    name?: string;
    taskModuleList?: string[];
    hosts?: string[];
  };

  type taskModuleOrchestrationDetailParams = {
    id: string;
  };

  type TaskModuleOrchestrationExecutePageVO = {
    id?: string;
    hostList?: HostPageVO[];
    taskModuleExecuteList?: TaskModuleExecutePageVO[];
    status?: string;
    executeTime?: string;
  };

  type taskModuleOrchestrationExecuteParams = {
    id: string;
  };

  type TaskModuleOrchestrationExecuteRequest = {
    hosts?: string[];
  };

  type TaskModuleOrchestrationExecuteVO = {
    id?: string;
    hostList?: HostVO[];
    taskModuleExecuteList?: TaskModuleExecuteVO[];
    status?: string;
    executeTime?: string;
  };

  type TaskModuleOrchestrationPageVO = {
    id?: string;
    name?: string;
    taskModuleList?: TaskModulePageVO[];
    hostList?: HostPageVO[];
  };

  type taskModuleOrchestrationPreTaskExecuteParams = {
    id: string;
  };

  type taskModuleOrchestrationResultPageParams = {
    id: string;
  };

  type TaskModuleOrchestrationResultPageRequest = {
    /** 筛选 */
    filter?: Record<string, any>;
    /** 搜索 */
    search?: Record<string, any>;
    page?: PageRequest;
  };

  type taskModuleOrchestrationResultParams = {
    id: string;
  };

  type taskModuleOrchestrationUpdateParams = {
    id: string;
  };

  type TaskModuleOrchestrationVO = {
    id?: string;
    name?: string;
    taskModuleList?: TaskModuleVO[];
    hostList?: HostVO[];
  };

  type TaskModulePageRequest = {
    /** 筛选 */
    filter?: Record<string, any>;
    /** 搜索 */
    search?: Record<string, any>;
    page?: PageRequest;
  };

  type TaskModulePageVO = {
    id?: string;
    name?: string;
    description?: string;
  };

  type taskModuleUpdateByIdParams = {
    id: string;
  };

  type TaskModuleVO = {
    id?: string;
    name?: string;
    preInspectionTaskId?: string;
    paramsMergeType?: string;
    params?: string;
    description?: string;
    taskModuleFront?: JsonNode;
  };

  type TaskResultVO = {
    id?: string;
    taskId?: string;
    atomicTask?: AtomicTaskVO;
    params?: string;
    log?: string;
    status?: string;
    count?: string[];
    success?: string[];
    failed?: string[];
  };

  type Template = {
    templateid?: string;
    host?: string;
    description?: string;
    name?: string;
    uuid?: string;
  };

  type TemplateCreateRequest = {
    templateid?: string;
    host?: string;
    description?: string;
    name?: string;
    uuid?: string;
    groups?: HostGroup[];
    tags?: TemplateTag[];
    templates?: Template[];
    macros?: HostMacro[];
  };

  type TemplateGetDTO = {
    ipmi_privilege?: string;
    maintenance_status?: string;
    tls_psk_identity?: string;
    flags?: string;
    description?: string;
    tls_issuer?: string;
    uuid?: string;
    auto_compress?: string;
    proxy_hostid?: string;
    maintenanceid?: string;
    maintenance_from?: string;
    ipmi_authtype?: string;
    ipmi_username?: string;
    host?: string;
    tls_psk?: string;
    custom_interfaces?: string;
    proxy_address?: string;
    maintenance_type?: string;
    tls_accept?: string;
    templateid?: string;
    lastaccess?: string;
    ipmi_password?: string;
    name?: string;
    tls_connect?: string;
    tls_subject?: string;
    status?: string;
    jmx_available?: string;
    errors_from?: string;
    available?: string;
    snmp_errors_from?: string;
    error?: string;
    jmx_errors_from?: string;
    snmp_disable_until?: string;
    jmx_error?: string;
    jmx_disable_until?: string;
    disable_until?: string;
    ipmi_errors_from?: string;
    snmp_error?: string;
    snmp_available?: string;
    ipmi_available?: string;
    ipmi_error?: string;
    ipmi_disable_until?: string;
  };

  type TemplateGetRequest = {
    countOutput?: boolean;
    editable?: boolean;
    excludeSearch?: boolean;
    filter?: string | string[];
    limit?: number;
    output?: string | string[];
    preservekeys?: boolean;
    search?: string | string[];
    searchByAny?: boolean;
    searchWildcardsEnabled?: boolean;
    sortorder?: string;
    startSearch?: boolean;
    templateids?: string[];
    groupids?: string[];
    parentTemplateids?: string[];
    hostids?: string[];
    graphids?: string[];
    itemids?: string[];
    triggerids?: string[];
    with_items?: boolean;
    with_triggers?: boolean;
    with_graphs?: boolean;
    with_httptests?: boolean;
    evaltype?: number;
    tags?: string[];
    selectGroups?: string;
    selectTags?: string;
    selectHosts?: string;
    selectTemplates?: string;
    selectParentTemplates?: string;
    selectHttpTests?: string;
    selectItems?: string;
    selectDiscoveries?: string;
    selectTriggers?: string;
    selectGraphs?: string;
    selectMacros?: string;
    selectDashboards?: string;
    selectValueMaps?: string;
    limitSelects?: number;
    sortfield?: string[];
  };

  type TemplateIdDTO = {
    templateids?: string[];
  };

  type TemplateMassreAddRequest = {
    templates?: Template[];
    groups?: HostGroup[];
    macros?: HostMacro[];
    templates_link?: Template[];
  };

  type TemplateMassreMoveRequest = {
    templateids?: string[];
    groupids?: string[];
    macros?: string[];
    templateids_clear?: string[];
    templateids_link?: string[];
  };

  type TemplateMassreUpdateRequest = {
    templates?: Template[];
    groups?: HostGroup[];
    macros?: HostMacro[];
    templates_clear?: Template[];
    templates_link?: Template[];
  };

  type TemplateTag = {
    tag?: string;
    value?: string;
  };

  type TemplateUpdateRequest = {
    templateid?: string;
    host?: string;
    description?: string;
    name?: string;
    uuid?: string;
    groups?: HostGroup[];
    tags?: TemplateTag[];
    macros?: HostMacro[];
    templates?: Template[];
    templates_clear?: Template[];
  };

  type TodayWarning = {
    count?: number;
    warning?: number;
    unresolvedWarning?: number;
  };

  type TopologyCURequest = {
    name?: string;
    description?: string;
    /** 拓扑图json */
    topologyJson?: Record<string, any>;
  };

  type TopologyPageRequest = {
    /** 筛选 */
    filter?: Record<string, any>;
    /** 搜索 */
    search?: Record<string, any>;
    page?: PageRequest;
  };

  type TopologyPageVO = {
    id?: string;
    name?: string;
    description?: string;
    operator?: Operator;
  };

  type TopologyVO = {
    id?: string;
    name?: string;
    description?: string;
    operator?: Operator;
    /** 拓扑图json */
    topologyJson?: Record<string, any>;
  };

  type updateGroupIconParams = {
    groupId: string;
  };

  type updateObjectIconParams = {
    objectId: string;
  };

  type updatePanoramicGroupParams = {
    id: string;
  };

  type updateTopologyParams = {
    id: string;
  };

  type User = {
    userid?: string;
  };

  type UserAuthenticationRequest = {
    extend?: boolean;
    sessionid?: string;
  };

  type UserAuthenticationResponse = {
    autologin?: string;
    debug_mode?: number;
    timezone?: string;
    roleid?: string;
    userdirectoryid?: string;
    attempt_clock?: string;
    refresh?: string;
    attempt_failed?: string;
    sessionid?: string;
    type?: number;
    userid?: string;
    attempt_ip?: string;
    url?: string;
    gui_access?: number;
    rows_per_page?: string;
    surname?: string;
    autologout?: string;
    name?: string;
    userip?: string;
    theme?: string;
    lang?: string;
    username?: string;
  };

  type UserCreateRequest = {
    /** 用户名称 */
    username?: string;
    /** 是否允许自动登录.
可用值:
0 - (default) 禁止自动登录;
1 - 允许自动登录.
 */
    autologin?: number;
    /** 会话过期时长. 接受具有后缀的秒或时间单位. 如果设置为 0s, 会话将永不过期.
默认: 15m.
 */
    autologout?: string;
    /** 用户语言代码, 示例, en_GB.
默认: default - 系统默认语言.
 */
    lang?: string;
    /** 自动刷新间隔. 接受具有后缀的秒或时间单位.
默认: 30s.
 */
    refresh?: string;
    /** 每页显示的对象条目.
默认: 50.
 */
    rowsPerPage?: number;
    /** 姓. */
    surname?: string;
    /** 用户的主题.
可用值:
default - (default) 系统默认主题;
blue-theme - 蓝主题;
dark-theme - 黑主题.
 */
    theme?: string;
    /** 用户登录后重定向页面的URL.
     */
    url?: string;
    /** 用户时区, 示例, Europe/London, UTC.
默认: default - 系统默认时区.
 */
    timezone?: string;
    /** 用户的角色ID. */
    roleid?: string;
    /** 用户的密码。
如果用户仅添加到具有 LDAP 访问权限的组，则可以省略。
 */
    passwd?: string;
    /** 要将用户添加到的用户 组。
用户组必须具有已定义的 usrgrpid 属性。
 */
    usrgrps?: Usrgrp[];
    /** 要创建的用户 media */
    medias?: Media[];
  };

  type UserGroupCreateRequest = {
    usrgrpid?: string;
    name?: string;
    debug_mode?: number;
    gui_access?: number;
    users_status?: number;
    rights?: Auth[];
    tag_filters?: TagAuth[];
    users?: User[];
  };

  type UserGroupDTO = {
    usrgrpid?: string;
    name?: string;
    debug_mode?: number;
    gui_access?: number;
    users_status?: number;
    users?: UserInfoDTO[];
    rights?: Auth[];
  };

  type UserGroupGetReqeust = {
    countOutput?: boolean;
    editable?: boolean;
    excludeSearch?: boolean;
    filter?: string | string[];
    limit?: number;
    output?: string | string[];
    preservekeys?: boolean;
    search?: string | string[];
    searchByAny?: boolean;
    searchWildcardsEnabled?: boolean;
    sortorder?: string;
    startSearch?: boolean;
    status?: number;
    userids?: string[];
    usrgrpids?: string[];
    selectTagFilters?: string;
    selectUsers?: string;
    selectRights?: string;
    limitSelects?: number;
    sortfield?: string[];
  };

  type UserGroupIdDTO = {
    usrgrpids?: string[];
  };

  type UserGroupUpdateRequest = {
    usrgrpid?: string;
    name?: string;
    debug_mode?: number;
    gui_access?: number;
    users_status?: number;
    rights?: Auth[];
    tag_filters?: TagAuth[];
    users?: User[];
  };

  type UserIdsResponse = {
    userids?: string[];
  };

  type UserInfoDTO = {
    autologin?: string;
    role?: Role;
    timezone?: string;
    users_status?: string;
    roleid?: string;
    attempt_clock?: string;
    refresh?: string;
    attempt_failed?: string;
    userid?: string;
    gui_access?: string;
    debug_mode?: string;
    attempt_ip?: string;
    url?: string;
    usrgrps?: UsrgrpsItem[];
    medias?: MediasItem[];
    rows_per_page?: string;
    surname?: string;
    autologout?: string;
    name?: string;
    theme?: string;
    lang?: string;
    username?: string;
    mediatypes?: MediatypesItem[];
  };

  type UserInfoRequest = {
    countOutput?: boolean;
    editable?: boolean;
    excludeSearch?: boolean;
    filter?: string | string[];
    limit?: number;
    output?: string | string[];
    preservekeys?: boolean;
    search?: string | string[];
    searchByAny?: boolean;
    searchWildcardsEnabled?: boolean;
    sortorder?: string;
    startSearch?: boolean;
    mediaids?: string[];
    mediatypeids?: string[];
    userids?: string[];
    usrgrpids?: string[];
    getAccess?: boolean;
    selectMedias?: string;
    selectMediatypes?: string;
    selectUsrgrps?: string;
    selectRole?: string;
    sortfield?: string[];
  };

  type UserInfoResponse = {
    autologin?: string;
    role?: Role;
    timezone?: string;
    users_status?: string;
    roleid?: string;
    attempt_clock?: string;
    refresh?: string;
    attempt_failed?: string;
    userid?: string;
    gui_access?: string;
    debug_mode?: string;
    attempt_ip?: string;
    url?: string;
    usrgrps?: UsrgrpsItem[];
    medias?: MediasItem[];
    rows_per_page?: string;
    surname?: string;
    autologout?: string;
    name?: string;
    theme?: string;
    lang?: string;
    username?: string;
    mediatypes?: MediatypesItem[];
  };

  type UserUpdateRequest = {
    /** 用户id */
    userid?: string;
    /** 用户名称 */
    username?: string;
    /** 是否允许自动登录.
可用值:
0 - (default) 禁止自动登录;
1 - 允许自动登录.
 */
    autologin?: number;
    /** 会话过期时长. 接受具有后缀的秒或时间单位. 如果设置为 0s, 会话将永不过期.
默认: 15m.
 */
    autologout?: string;
    /** 用户语言代码, 示例, en_GB.
默认: default - 系统默认语言.
 */
    lang?: string;
    /** 自动刷新间隔. 接受具有后缀的秒或时间单位.
默认: 30s.
 */
    refresh?: string;
    /** 每页显示的对象条目.
默认: 50.
 */
    rowsPerPage?: number;
    /** 姓. */
    surname?: string;
    /** 用户的主题.
可用值:
default - (default) 系统默认主题;
blue-theme - 蓝主题;
dark-theme - 黑主题.
 */
    theme?: string;
    /** 用户登录后重定向页面的URL.
     */
    url?: string;
    /** 用户时区, 示例, Europe/London, UTC.
默认: default - 系统默认时区.
 */
    timezone?: string;
    /** 用户的角色ID. */
    roleid?: string;
    /** 用户的密码。
如果用户仅添加到具有 LDAP 访问权限的组，则可以省略。
 */
    passwd?: string;
    /** 要将用户添加到的用户 组。
用户组必须具有已定义的 usrgrpid 属性。
 */
    usrgrps?: Usrgrp[];
    /** 要创建的用户 media */
    medias?: Media[];
  };

  type UserVO = {
    id?: string;
    username?: string;
    phone?: string;
    email?: string;
    enable?: boolean;
  };

  type Usrgrp = {
    usrgrpid?: string;
  };

  type UsrgrpsItem = {
    debug_mode?: string;
    usrgrpid?: string;
    userdirectoryid?: string;
    name?: string;
    users_status?: string;
    gui_access?: string;
  };

  type WarningCount = {
    notClassified?: number;
    information?: number;
    warning?: number;
    average?: number;
    high?: number;
    disaster?: number;
  };

  type WarningTop = {
    warning?: number;
    hostGroup?: string;
  };
}
