// 新建监控对象类型模态框
import BaseContext from '@/Context/BaseContext';
import { PlusOutlined } from '@ant-design/icons';
import { ModalForm } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { An<PERSON>, Button, Col, message, Row, Spin } from 'antd';
import { memo, useContext, useState } from 'react';
import styles from './index.less';
import TypeCard from './TypeCard';

const avatarMap: { [key: string]: any } = {
  default: require('./img/linux.png'),
  AGENT_LINUX: require('./img/linux.png'),
  ORACLE: require('./img/oracle.png'),
  OCEAN_BASE: require('./img/oceanbase.jpeg'),
};

const CreateTypeModal = () => {
  const [hostTypeId, setHostTypeId] = useState('');

  const [messageApi, contextHolder] = message.useMessage();

  const { objectList, objectListLoading } = useContext(BaseContext);

  const handleSubTypeClick = (id: string) => {
    setHostTypeId(id);
  };

  const renderSubTypes = (subTypes: API.MonitorObjectSubtypeVO[]) => {
    return subTypes.map((child) => (
      <TypeCard
        name={child.name || ''}
        avatar={avatarMap[child.type || 'default']}
        key={child.id}
        checked={hostTypeId === child.id}
        onClick={() => handleSubTypeClick(child.id!)}
      />
    ));
  };

  const showError = () => {
    messageApi.open({
      type: 'error',
      content: '请选择对象类型！',
    });
  };

  return (
    <ModalForm
      className={styles.modal}
      title="请选择监控对象类型"
      submitter={{
        render: (props) => {
          return [
            <Button
              key="submit"
              onClick={() => {
                props.submit();
              }}
              type="primary"
            >
              创建
            </Button>,
          ];
        },
      }}
      width={890}
      trigger={
        <Button key="add" type="primary" icon={<PlusOutlined />}>
          新增监控对象
        </Button>
      }
      onFinish={async () => {
        if (!hostTypeId) {
          showError();
          return false;
        }
        history.push(`/monitor-config/host/add/${hostTypeId}`);
        return true;
      }}
    >
      {contextHolder}
      {objectListLoading ? (
        <Spin spinning />
      ) : (
        <Row>
          <Col flex="120px">
            <Anchor
              items={
                objectList?.map((item) => ({
                  key: String(item.id ?? ''),
                  title: item.label,
                  href: `#type${item.id}`,
                })) || []
              }
              getContainer={() =>
                (document.querySelector('.ant-modal-body') as HTMLElement) || undefined
              }
            />
          </Col>
          <Col flex="auto">
            {objectList?.map((item) => (
              <div key={item.id} id={`type${item.id}`}>
                {renderSubTypes(item.subTypes || [])}
              </div>
            ))}
          </Col>
        </Row>
      )}
    </ModalForm>
  );
};

export default memo(CreateTypeModal);
