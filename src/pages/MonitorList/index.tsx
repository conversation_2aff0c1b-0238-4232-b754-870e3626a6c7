import { MONITOR_TYPE } from '@/enums';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import { zabbixList } from '@/services/zabbix';
import {
  bpsToHumanReadable,
  bytesToSize,
  countDecimalPlaces,
  formatSecondsToString,
  hzToHumanReadable,
  option2enum,
  queryPagingTable,
  syncToUrl,
} from '@/utils';
import { host, hostGroups } from '@/utils/column';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import { QuestionCircleOutlined } from '@ant-design/icons';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProConfigProvider,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { Link, useRequest } from '@umijs/max';
import { Space, Tag, Tooltip, Typography } from 'antd';
import dayjs from 'dayjs';
import React, { useRef } from 'react';
import { valueTypeMap } from '../HostMonitor/valueTypeMap';

export const findNewValue = (mappings: Record<string, any>[], value: string | number) => {
  const match = mappings.find((mapping) => mapping.value === value.toString());

  // 如果找到了匹配项，则返回newvalue，否则返回一个默认信息或null
  return match ? `${match.newvalue} (${value})` : value;
};
export const formatValue = (units: string, value: string | number) => {
  if (!value) return ' ';
  if (units === 'B') {
    return bytesToSize(value);
  }
  if (units === '%') {
    const val = Number(value);
    const decimalPart = countDecimalPlaces(val);
    return decimalPart ? `${val.toFixed(4)}${units}` : `${value}${units}`;
  }
  if (units === 'uptime') {
    return dayjs().startOf('day').second(Number(value)).format('HH:mm:ss');
  }

  if (units === 'bps') {
    return bpsToHumanReadable(value);
  }
  if (units === 'Hz') {
    return hzToHumanReadable(value);
  }

  return units ? `${Number(value).toFixed(4)}${units}` : value;
};

const MonitorList: React.FC = withStorageToUrl(({ queryParams }) => {
  const tableRef = useRef<ActionType | undefined>();
  const formRef = useRef<ProFormInstance>();

  const columns: ProColumns<RK_API.Item>[] = [
    { ...hostGroups, initialValue: queryParams.getAll('groupids') },
    { ...host, initialValue: queryParams.getAll('hostids') },
    {
      title: '主机',
      width: 150,
      dataIndex: 'hosts',
      hideInSearch: true,
      renderText: (text, record) => {
        return record?.hosts?.at(0)?.name;
      },
      fixed: 'left',
    },
    {
      title: '名称',
      dataIndex: 'name',
      render(dom, entity) {
        return (
          <Space>
            <Typography.Text>{dom}</Typography.Text>
            {entity?.description && (
              <Tooltip title={entity?.description || ''}>
                <QuestionCircleOutlined />
              </Tooltip>
            )}
          </Space>
        );
      },
      initialValue: queryParams.get('name'),
    },
    {
      title: '间隔',
      width: 80,
      dataIndex: 'delay',
      hideInSearch: true,
    },
    {
      title: '历史记录',
      width: 80,
      dataIndex: 'history',
      hideInSearch: true,
    },
    {
      title: '趋势',
      width: 80,
      dataIndex: 'trends',
      hideInSearch: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      valueType: 'select',
      width: 150,
      valueEnum: option2enum(MONITOR_TYPE),
      initialValue: queryParams.get('type'),
    },
    {
      title: '最近检查记录',
      dataIndex: 'lastclock',
      hideInSearch: true,
      // valueType: 'date',
      renderText(text) {
        if (!text) return text;
        const now = dayjs().unix();
        const seconds = now - text;
        const hours = seconds / 3600;
        // 仅显示过去24小时内的值
        const val = hours > 24 ? '-' : formatSecondsToString(seconds);
        const date = dayjs().subtract(seconds, 's').format('YYYY-MM-DD HH:mm:ss');
        return <Tooltip title={date}>{val}</Tooltip>;
      },
    },
    {
      title: '监控数据',
      dataIndex: 'lastvalue',
      hideInSearch: true,
      width: 120,
      renderText(text, entity) {
        const mappings = entity?.valuemap?.mappings || [];
        if (mappings.length) {
          return findNewValue(mappings, text);
        }

        return formatValue(entity?.units, text);
      },
    },
    {
      title: '更改',
      dataIndex: 'prevvalue',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
      renderText(text, entity) {
        const { lastvalue, prevvalue } = entity;

        return formatValue(entity?.units, Number(lastvalue) - Number(prevvalue));
      },
    },
    {
      title: '',
      dataIndex: 'itemid',
      width: 100,
      hideInSearch: true,
      render(dom, entity) {
        return <Link to={`trend-chart/${entity.itemid}`}>趋势图</Link>;
      },
    },
    {
      title: '标签',
      dataIndex: 'tags',
      colSize: 3,
      // @ts-ignore
      valueType: 'conditionSetter',
      search: {
        transform: (value) => ({
          tags: value?.conditionList || [],
          evaltype: value?.evaltype,
        }),
      },
      render(dom, entity) {
        const { tags = [] } = entity;
        if (!tags.length) return <>-</>;
        return (tags as RK_API.TemplateTag[]).map((item, index) => (
          <Tag key={index}>
            {item?.tag}: {item?.value}
          </Tag>
        ));
      },
    },
  ];
  // 获取监控列表
  const { run: getItems } = useRequest((params) => zabbixList(params), {
    manual: true,
  });

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProConfigProvider valueTypeMap={valueTypeMap}>
        <ProTable<RK_API.Item>
          {...defaultTableConfig}
          search={SearchOptionRender}
          onSubmit={syncToUrl}
          rowKey="itemid"
          formRef={formRef}
          actionRef={tableRef}
          columns={columns}
          headerTitle="监控列表"
          request={async (params) => {
            const { tags = [], evaltype, name, groupids = [], hostids = [], ...rest } = params;
            const res = await queryPagingTable(
              {
                evaltype,
                tags: tags.filter((item: RK_API.TemplateTag) => item.tag),
                groupids: groupids.length ? groupids : null,
                hostids: hostids.length ? hostids : null,
                sortfield: 'name',
                search: {
                  name,
                },
                filter: rest,
                method: 'item.get',
                output: ['value_type', 'hostid', 'itemid'],
                monitored: true,
              },
              zabbixList,
            );
            const itemIds = res.data?.map((item: RK_API.Item) => item.itemid) || [];
            const { current = 1, pageSize = 10 } = params;
            const index = (current - 1) * pageSize;

            const itemList = await getItems({
              evaltype,
              tags: tags.filter((item: RK_API.TemplateTag) => item.tag),
              groupids: groupids.length ? groupids : null,
              hostids: hostids.length ? hostids : null,
              sortfield: 'name',
              search: {
                name,
              },
              filter: rest,
              selectTags: 'extend',
              selectHosts: 'extend',
              selectPreprocessing: 'extend',
              selectItemDiscovery: 'extend',
              selectValueMap: ['mappings'],
              method: 'item.get',
              output: [
                'type',
                'value_type',
                'hosts',
                'hostid',
                'itemid',
                'name',
                'description',
                'delay',
                'history',
                'trends',
                'units',
                'lastclock',
                'lastvalue',
                'prevvalue',
                'tags',
                'itemDiscovery',
                'valuemap',
              ],
              itemids: itemIds.splice(index, pageSize),
            });

            return { ...res, data: itemList };
          }}
        />
      </ProConfigProvider>
    </PageContainer>
  );
});

export default MonitorList;
