import RKColumn from '@/components/Charts/Column';
import RkGauge from '@/components/Charts/Gauge';
import RkLiquid from '@/components/Charts/Liquid';
import RkPie from '@/components/Charts/Pie';
import { statsView } from '@/services/http/countView';
import { passParamsToPage, renderNumber } from '@/utils';
import { PageContainer } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Card, Col, Row } from 'antd';
import dayjs from 'dayjs';

const Dashboard = () => {
  const { data, loading } = useRequest<{
    data: API.StatisticsVO;
  }>(() => statsView());

  return (
    <PageContainer header={{ title: false }} loading={loading}>
      <Row gutter={[24, 24]}>
        <Col xs={24} sm={24} md={12} lg={12} xl={12}>
          <Card title="业务健康度" bordered={false}>
            <RkPie
              height={200}
              radius={0.8}
              innerRadius={0.7}
              showPercentValue
              innerTitle="健康率"
              tooltip={false}
              label={{
                type: 'spider',
                content: '{name}：{value}\n比例：{percentage}',
              }}
              innerContent={
                (
                  (renderNumber(data?.healthy?.normal) / renderNumber(data?.healthy?.count)) *
                  100
                ).toFixed(2) + '%'
              }
              data={[
                {
                  type: '告警',
                  value: renderNumber(data?.healthy?.warning),
                },
                {
                  type: '未告警',
                  value: renderNumber(data?.healthy?.normal),
                },
              ]}
            />
          </Card>
        </Col>
        <Col xs={24} sm={24} md={12} lg={12} xl={12}>
          <Card title="今日告警" bordered={false}>
            <Row gutter={[12, 12]}>
              <Col xs={24} sm={8} md={8} lg={8} xl={8}>
                <RkLiquid
                  radius={0.9}
                  height={200}
                  percent={1}
                  innerContent={`${renderNumber(data?.todayWarning?.count)}`}
                  innerTitle="告警总数"
                  style={{ cursor: 'pointer' }}
                  onEvent={(_, event) => {
                    if (event.type === 'click') {
                      passParamsToPage('/alarm/event', {
                        showStyle: 1,
                        date: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
                      });
                    }
                  }}
                />
              </Col>
              <Col xs={24} sm={8} md={8} lg={8} xl={8}>
                <RkLiquid
                  radius={0.9}
                  height={200}
                  percent={
                    renderNumber(data?.todayWarning?.warning) /
                    renderNumber(data?.todayWarning?.count)
                  }
                  innerContent={`${renderNumber(data?.todayWarning?.warning)}`}
                  innerTitle="已经恢复告警"
                  color="#73d13d"
                  style={{ cursor: 'pointer' }}
                  onEvent={(_, event) => {
                    if (event.type === 'click') {
                      passParamsToPage('/alarm/event', {
                        showStyle: 1,
                        date: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
                        recover: '1',
                      });
                    }
                  }}
                />
              </Col>
              <Col xs={24} sm={8} md={8} lg={8} xl={8}>
                <RkLiquid
                  radius={0.9}
                  height={200}
                  percent={
                    renderNumber(data?.todayWarning?.unresolvedWarning) /
                    renderNumber(data?.todayWarning?.count)
                  }
                  innerContent={`${renderNumber(data?.todayWarning?.unresolvedWarning)}`}
                  innerTitle="未恢复告警"
                  color="#ffc53d"
                  style={{ cursor: 'pointer' }}
                  onEvent={(_, event) => {
                    if (event.type === 'click') {
                      passParamsToPage('/alarm/event', {
                        showStyle: 0,
                        date: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
                        // recover: '0',
                      });
                    }
                  }}
                />
              </Col>
            </Row>
          </Card>
        </Col>

        <Col xs={24} sm={24} md={12} lg={12} xl={12}>
          <Card title="告警统计" bordered={false}>
            <RkPie
              color={['#D6EFED', '#B7D3DF', '#FFDCA9', '#FAAB78', '#FF8787', '#C63D2F']}
              height={250}
              radius={0.85}
              innerRadius={0.7}
              innerTitle="告警总数"
              data={[
                {
                  type: '未分类',
                  value: renderNumber(data?.warningCount?.notClassified),
                },
                {
                  type: '信息',
                  value: renderNumber(data?.warningCount?.information),
                },
                {
                  type: '警告',
                  value: renderNumber(data?.warningCount?.warning),
                },
                {
                  type: '一般严重',
                  value: renderNumber(data?.warningCount?.average),
                },
                {
                  type: '严重',
                  value: renderNumber(data?.warningCount?.high),
                },
                {
                  type: '灾难',
                  value: renderNumber(data?.warningCount?.disaster),
                },
              ]}
            />
          </Card>
        </Col>

        <Col xs={24} sm={24} md={12} lg={12} xl={12}>
          <Card title="告警排行榜top10" bordered={false}>
            <RKColumn
              height={250}
              xField="hostGroup"
              yField="warning"
              data={data?.warningTops || []}
              theme="custom-theme-cold"
            />
          </Card>
        </Col>

        {/* 添加系统性能指标仪表盘 */}
        <Col span={24}>
          <Card title="系统性能指标" bordered={false}>
            <Row gutter={[24, 24]}>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <RkGauge
                  percent={
                    renderNumber(data?.healthy?.normal) / renderNumber(data?.healthy?.count) || 0
                  }
                  title="系统健康度"
                  height={200}
                />
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <RkGauge
                  percent={Math.min(
                    1,
                    renderNumber(data?.todayWarning?.warning) /
                      Math.max(1, renderNumber(data?.todayWarning?.count)) || 0,
                  )}
                  title="告警恢复率"
                  height={200}
                />
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <RkGauge
                  percent={Math.min(
                    1,
                    (renderNumber(data?.warningCount?.information) +
                      renderNumber(data?.warningCount?.warning)) /
                      Math.max(
                        1,
                        renderNumber(data?.warningCount?.information) +
                          renderNumber(data?.warningCount?.warning) +
                          renderNumber(data?.warningCount?.average) +
                          renderNumber(data?.warningCount?.high) +
                          renderNumber(data?.warningCount?.disaster),
                      ) || 0,
                  )}
                  title="低级别告警占比"
                  height={200}
                />
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <RkGauge
                  percent={
                    data?.deviceStatistics?.length
                      ? Math.min(1, data.deviceStatistics.length / 100)
                      : 0
                  }
                  title="设备覆盖率"
                  height={200}
                />
              </Col>
            </Row>
          </Card>
        </Col>
        <Col xs={24} sm={24} md={24} lg={24} xl={24}>
          <Card title="设备统计" bordered={false}>
            <RKColumn
              color="#63daab"
              height={250}
              meta={{
                type: {
                  alias: '对象群组',
                },
                count: {
                  alias: '主机数',
                },
              }}
              xField="group"
              yField="count"
              data={data?.deviceStatistics || []}
              slider={{
                start: 0,
                end: data?.deviceStatistics?.length ? 30 / data?.deviceStatistics.length : 0,
              }}
            />
          </Card>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default Dashboard;
