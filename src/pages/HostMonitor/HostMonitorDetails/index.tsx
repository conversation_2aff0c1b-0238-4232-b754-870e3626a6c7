import RkArea from '@/components/Charts/Area';
import Gauge from '@/components/Charts/Gauge';
import GradientBar from '@/components/Charts/GradientBar';
import RkLine from '@/components/Charts/Line';
import RkRingProgress from '@/components/Charts/RingProgress';
import { AVAILABLE, HOST_STATUS } from '@/enums';
import { zabbix, zabbixPost } from '@/services/zabbix';
import { formatPercent, option2enum } from '@/utils';
import { PageContainer, ProCard, ProDescriptions } from '@ant-design/pro-components';
import { useParams, useRequest } from '@umijs/max';
import { Col, Row, Space, Tag } from 'antd';
const { TabPane } = ProCard;

import classNames from 'classnames/bind';
import styles from './index.less';
import { areaData, diskIOData, networkTrafficData } from './mock';
const cx = classNames.bind(styles);
const HostMonitorDetails = () => {
  const { id } = useParams();
  const { data: hostData, loading } = useRequest(() =>
    zabbix({
      hostids: [id],
      selectGroups: 'extend',
      selectTags: 'extend',
      selectInheritedTags: 'extend',
      method: 'host.get',
    }),
  );

  //  cpu使用率(CPU)
  const {} = useRequest(
    () =>
      zabbixPost({
        method: 'item.get',
        hostids: [id],
        filter: {
          key_: 'system.cpu',
        },
        output: ['name', 'lastvalue', 'units'],
      }),
    {
      // pollingInterval: 5000,
    },
  );
  return (
    <PageContainer header={{ title: false }}>
      <ProCard
        tabs={{
          type: 'card',
        }}
        loading={loading}
        className={cx('tabs-card')}
      >
        <TabPane key="tab1" tab="概览">
          <ProCard
            loading={loading}
            title="基础信息"
            style={{
              backgroundColor: '#fff',
              borderRadius: '0 0 4px 4px',
            }}
          >
            <ProDescriptions column={4} dataSource={hostData?.at(0) || {}}>
              <ProDescriptions.Item label="名称" dataIndex="name" />
              <ProDescriptions.Item label="可见的名称" dataIndex="host" />

              <ProDescriptions.Item
                label="对象组"
                dataIndex="groups"
                renderText={(_, record) => {
                  return record?.groups?.map((item: RK_API.HostGroup) => item.name).join(',');
                }}
              />

              <ProDescriptions.Item label="类型" dataIndex={['rkzl_type', 'name']} />
              <ProDescriptions.Item label="子类型" dataIndex={['rkzl_type', 'name']} />

              <ProDescriptions.Item
                label="状态"
                valueEnum={option2enum(HOST_STATUS)}
                dataIndex="status"
              />
              <ProDescriptions.Item
                label="可用性"
                dataIndex="active_available"
                valueEnum={option2enum(AVAILABLE)}
              />
              <ProDescriptions.Item
                label="标签"
                dataIndex="tags"
                render={(_, record) => {
                  const { tags = [], inheritedTags = [] } = record;
                  const mixTags = [...inheritedTags, ...tags];
                  return (
                    <Space wrap size={[0, 8]}>
                      {(mixTags as RK_API.TemplateTag[]).map((item, index) => (
                        <Tag key={index}>
                          {item?.tag}: {item?.value}
                        </Tag>
                      ))}
                    </Space>
                  );
                }}
              />
            </ProDescriptions>
          </ProCard>
          <ProCard
            loading={loading}
            title="CPU 使用率"
            style={{
              backgroundColor: '#fff',
              marginBlockStart: 16,
            }}
            bodyStyle={{
              padding: '24px',
            }}
          >
            <Row align="middle" wrap={false}>
              <Col flex="360px">
                <Gauge
                  percent={0.75}
                  title="CPU总使用率"
                  width={300}
                  height={200}
                  autoFit={false}
                />
              </Col>
              <Col flex="40px" />

              <Col flex="auto">
                <RkArea
                  data={areaData}
                  xField="Date"
                  yField="scales"
                  height={200}
                  color="#37c8c7"
                />
              </Col>
            </Row>
          </ProCard>
          <ProCard
            loading={loading}
            title="内存使用率"
            style={{
              backgroundColor: '#fff',
              marginBlockStart: 16,
            }}
            bodyStyle={{
              padding: '24px',
            }}
          >
            <Row align="middle" wrap={false}>
              <Col flex="360px">
                <Gauge
                  percent={0.5}
                  title="内存使用率"
                  width={300}
                  height={200}
                  autoFit={false}
                  info="100M/200M"
                  titleOffsetY={-36}
                  contentOffsetY={-16}
                />
              </Col>
              <Col flex="40px" />

              <Col flex="auto">
                <RkArea
                  data={areaData}
                  xField="Date"
                  yField="scales"
                  height={200}
                  color="#37c8c7"
                />
              </Col>
            </Row>
          </ProCard>
          <Row gutter={16}>
            <Col span={16}>
              <ProCard
                style={{
                  backgroundColor: '#fff',
                  marginBlockStart: 16,
                }}
                title="文件系统利用率"
                loading={loading}
                bodyStyle={{
                  padding: '24px',
                  height: 265,
                  boxSizing: 'content-box',
                }}
              >
                <GradientBar
                  xField="value"
                  yField="type"
                  data={[
                    {
                      type: '/dev/sda1',
                      value: 0.08,
                    },
                    {
                      type: '/dev/sda2',
                      value: 0.52,
                    },
                    {
                      type: '/dev/sda3',
                      value: 0.61,
                    },
                    {
                      type: '/dev/sda4',
                      value: 0.45,
                    },
                    {
                      type: '/dev/sda5',
                      value: 0.48,
                    },
                  ]}
                  label={{
                    position: 'right',
                    offset: 8,
                    formatter: (datum) => {
                      const percent = formatPercent(datum.value);
                      return `${percent}   可用空间：${datum.raw ?? 0}G `;
                    },
                  }}
                />
              </ProCard>
            </Col>
            <Col span={8}>
              <ProCard
                style={{
                  backgroundColor: '#fff',
                  marginBlockStart: 16,
                }}
                title="交换分区（swap）"
                loading={loading}
                bodyStyle={{
                  padding: '0 24px 48px 24px',
                  height: 265,
                  boxSizing: 'content-box',
                }}
              >
                <RkRingProgress
                  height={200}
                  radius={0.8}
                  innerRadius={0.8}
                  color={['rgba(56, 204, 204, 1)', 'rgba(245, 245, 245, 1)']}
                  percent={0.8}
                  statistic={{
                    title: {
                      style: {
                        color: '#101010',
                        fontSize: '16px',
                        lineHeight: '16px',
                        fontWeight: 400,
                        textShadow: 'none',
                      },
                      formatter: () => '交换分区使用情况',
                      offsetY: 150,
                    },
                    content: {
                      style: {
                        color: '#101010',
                        fontSize: '28px',
                        fontWeight: 500,
                      },
                      offsetY: -20,
                      formatter: (datum) => formatPercent(datum?.percent || 0),
                    },
                  }}
                />
              </ProCard>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <ProCard
                style={{
                  backgroundColor: '#fff',
                  marginBlockStart: 16,
                }}
                title="磁盘 I/O 读写速度"
                loading={loading}
                bodyStyle={{
                  padding: 24,
                  height: 265,
                  boxSizing: 'content-box',
                }}
              >
                <RkLine
                  data={diskIOData}
                  xField="hour"
                  yField="value"
                  seriesField="category"
                  height={200}
                  color={(datum) => {
                    if (datum?.category === '读速度') {
                      return '#4095E5';
                    }
                    return '#68BBC4';
                  }}
                  legend={{
                    position: 'top',
                    marker: {
                      symbol: 'hyphen',
                    },
                  }}
                  point={{
                    size: 2,
                    shape: 'dot',
                    style: {
                      fill: 'white',
                      lineWidth: 2,
                    },
                  }}
                />
              </ProCard>
            </Col>
            <Col span={12}>
              <ProCard
                style={{
                  backgroundColor: '#fff',
                  marginBlockStart: 16,
                }}
                title="网络流量趋势图"
                loading={loading}
                bodyStyle={{
                  padding: 24,
                  height: 265,
                  boxSizing: 'content-box',
                }}
              >
                <RkLine
                  data={networkTrafficData}
                  xField="hour"
                  yField="value"
                  seriesField="category"
                  height={200}
                  color={(datum) => {
                    if (datum?.category === '流出') {
                      return '#4095E5';
                    }
                    return '#68BBC4';
                  }}
                  legend={{
                    position: 'top',
                    marker: {
                      symbol: 'hyphen',
                    },
                  }}
                  point={{
                    size: 2,
                    shape: 'dot',
                    style: {
                      fill: 'white',
                      lineWidth: 2,
                    },
                  }}
                />
              </ProCard>
            </Col>
          </Row>
        </TabPane>
      </ProCard>
    </PageContainer>
  );
};

export default HostMonitorDetails;
