import Gauge from '@/components/Charts/Gauge';
import { AVAILABLE, HOST_STATUS } from '@/enums';
import { zabbix } from '@/services/zabbix';
import { option2enum } from '@/utils';
import { PageContainer, ProCard, ProDescriptions } from '@ant-design/pro-components';
import { useParams, useRequest } from '@umijs/max';
import { Col, Row, Tag } from 'antd';
const { TabPane } = ProCard;

import classNames from 'classnames/bind';
import styles from './index.less';
const cx = classNames.bind(styles);
const HostMonitorDetails = () => {
  const { id } = useParams();
  const { data: hostData, loading } = useRequest(() =>
    zabbix({
      hostids: [id],
      selectGroups: 'extend',
      selectTags: 'extend',
      selectInheritedTags: 'extend',
      method: 'host.get',
    }),
  );
  return (
    <PageContainer header={{ title: false }}>
      <ProCard
        tabs={{
          type: 'card',
        }}
        loading={loading}
        className={cx('tabs-card')}
      >
        <TabPane key="tab1" tab="概览">
          <ProCard
            loading={loading}
            title="基础信息"
            style={{
              backgroundColor: '#fff',
              borderRadius: '0 0 4px 4px',
            }}
          >
            <ProDescriptions column={4} dataSource={hostData?.at(0) || {}}>
              <ProDescriptions.Item label="名称" dataIndex="name" />
              <ProDescriptions.Item label="可见的名称" dataIndex="host" />

              <ProDescriptions.Item
                label="对象组"
                dataIndex="groups"
                renderText={(_, record) => {
                  return record?.groups?.map((item: RK_API.HostGroup) => item.name).join(',');
                }}
              />

              <ProDescriptions.Item
                label="类型"
                dataIndex="rkzl_type"
                renderText={(_, record) => {
                  return record?.rkzl_type ? `${record?.rkzl_type?.name}` : '-';
                }}
              />
              <ProDescriptions.Item
                label="子类型"
                dataIndex="rkzl_type"
                renderText={(_, record) => {
                  return record?.rkzl_subtype ? `${record?.rkzl_subtype?.name}` : '-';
                }}
              />

              <ProDescriptions.Item
                label="状态"
                valueEnum={option2enum(HOST_STATUS)}
                dataIndex="status"
              />
              <ProDescriptions.Item
                label="可用性"
                dataIndex="active_available"
                valueEnum={option2enum(AVAILABLE)}
              />
              <ProDescriptions.Item
                label="标签"
                dataIndex="tags"
                render={(_, record) => {
                  const { tags = [], inheritedTags = [] } = record;
                  const mixTags = [...inheritedTags, ...tags];
                  return (mixTags as RK_API.TemplateTag[]).map((item, index) => (
                    <Tag key={index}>
                      {item?.tag}: {item?.value}
                    </Tag>
                  ));
                }}
              />
            </ProDescriptions>
          </ProCard>
          <ProCard
            loading={loading}
            title="CPU 使用率"
            style={{
              backgroundColor: '#fff',
              marginBlockStart: 16,
            }}
            bodyStyle={{
              height: 260,
            }}
          >
            <Row gutter={[24, 24]}>
              <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                <Gauge percent={0.65} title="CPU 使用率" height={220} />
              </Col>
              <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                {/* 这里可以添加其他内容，比如CPU详细信息或其他指标 */}
              </Col>
            </Row>
          </ProCard>
        </TabPane>
      </ProCard>
    </PageContainer>
  );
};

export default HostMonitorDetails;
