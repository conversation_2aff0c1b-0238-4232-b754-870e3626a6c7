export const areaData = [
  {
    Date: '2010-01',
    scales: 1998,
  },
  {
    Date: '2010-02',
    scales: 1850,
  },
  {
    Date: '2010-03',
    scales: 1720,
  },
  {
    Date: '2010-04',
    scales: 1818,
  },
  {
    Date: '2010-05',
    scales: 1920,
  },
  {
    Date: '2010-06',
    scales: 1802,
  },
  {
    Date: '2010-07',
    scales: 1945,
  },
  {
    Date: '2010-08',
    scales: 1856,
  },
  {
    Date: '2010-09',
    scales: 2107,
  },
  {
    Date: '2010-10',
    scales: 2140,
  },
  {
    Date: '2010-11',
    scales: 2311,
  },
  {
    Date: '2010-12',
    scales: 1972,
  },
];

export const diskIOData = [
  // Midnight - low activity
  { hour: '00:00', value: 85, category: '读速度' },
  { hour: '00:00', value: 45, category: '写速度' },

  // Early morning - gradual increase
  { hour: '01:00', value: 90, category: '读速度' },
  { hour: '01:00', value: 50, category: '写速度' },
  { hour: '02:00', value: 95, category: '读速度' },
  { hour: '02:00', value: 55, category: '写速度' },
  { hour: '03:00', value: 110, category: '读速度' },
  { hour: '03:00', value: 65, category: '写速度' },

  // Morning peak - highest activity
  { hour: '04:00', value: 150, category: '读速度' },
  { hour: '04:00', value: 90, category: '写速度' },
  { hour: '05:00', value: 180, category: '读速度' },
  { hour: '05:00', value: 110, category: '写速度' },
  { hour: '06:00', value: 210, category: '读速度' },
  { hour: '06:00', value: 130, category: '写速度' },
  { hour: '07:00', value: 240, category: '读速度' },
  { hour: '07:00', value: 150, category: '写速度' },
  { hour: '08:00', value: 260, category: '读速度' },
  { hour: '08:00', value: 180, category: '写速度' },

  // Midday - sustained high activity
  { hour: '09:00', value: 250, category: '读速度' },
  { hour: '09:00', value: 170, category: '写速度' },
  { hour: '10:00', value: 230, category: '读速度' },
  { hour: '10:00', value: 160, category: '写速度' },
  { hour: '11:00', value: 220, category: '读速度' },
  { hour: '11:00', value: 150, category: '写速度' },

  // Afternoon - gradual decrease
  { hour: '12:00', value: 200, category: '读速度' },
  { hour: '12:00', value: 130, category: '写速度' },
  { hour: '13:00', value: 190, category: '读速度' },
  { hour: '13:00', value: 120, category: '写速度' },
  { hour: '14:00', value: 180, category: '读速度' },
  { hour: '14:00', value: 110, category: '写速度' },
  { hour: '15:00', value: 170, category: '读速度' },
  { hour: '15:00', value: 100, category: '写速度' },

  // Evening - lower activity
  { hour: '16:00', value: 150, category: '读速度' },
  { hour: '16:00', value: 90, category: '写速度' },
  { hour: '17:00', value: 130, category: '读速度' },
  { hour: '17:00', value: 80, category: '写速度' },
  { hour: '18:00', value: 110, category: '读速度' },
  { hour: '18:00', value: 70, category: '写速度' },

  // Night - lowest activity
  { hour: '19:00', value: 100, category: '读速度' },
  { hour: '19:00', value: 60, category: '写速度' },
  { hour: '20:00', value: 95, category: '读速度' },
  { hour: '20:00', value: 55, category: '写速度' },
  { hour: '21:00', value: 90, category: '读速度' },
  { hour: '21:00', value: 50, category: '写速度' },
  { hour: '22:00', value: 85, category: '读速度' },
  { hour: '22:00', value: 45, category: '写速度' },
  { hour: '23:00', value: 80, category: '读速度' },
  { hour: '23:00', value: 40, category: '写速度' },
];

export const networkTrafficData = [
  { hour: '00:00', value: 25, category: '流入' },
  { hour: '00:00', value: 15, category: '流出' },

  { hour: '01:00', value: 20, category: '流入' },
  { hour: '01:00', value: 12, category: '流出' },

  { hour: '02:00', value: 18, category: '流入' },
  { hour: '02:00', value: 10, category: '流出' },

  { hour: '03:00', value: 15, category: '流入' },
  { hour: '03:00', value: 8, category: '流出' },

  { hour: '04:00', value: 30, category: '流入' },
  { hour: '04:00', value: 20, category: '流出' },

  { hour: '05:00', value: 50, category: '流入' },
  { hour: '05:00', value: 35, category: '流出' },

  { hour: '06:00', value: 80, category: '流入' },
  { hour: '06:00', value: 60, category: '流出' },

  { hour: '07:00', value: 120, category: '流入' },
  { hour: '07:00', value: 90, category: '流出' },

  { hour: '08:00', value: 150, category: '流入' },
  { hour: '08:00', value: 110, category: '流出' },

  { hour: '09:00', value: 180, category: '流入' },
  { hour: '09:00', value: 130, category: '流出' },

  { hour: '10:00', value: 200, category: '流入' },
  { hour: '10:00', value: 150, category: '流出' },

  { hour: '11:极速', value: 220, category: '流入' },
  { hour: '11:00', value: 170, category: '流出' },

  { hour: '12:00', value: 210, category: '流入' },
  { hour: '12:00', value: 160, category: '流出' },

  { hour: '13:00', value: 190, category: '流入' },
  { hour: '13:00', value: 140, category: '流出' },

  { hour: '14:00', value: 170, category: '流入' },
  { hour: '14:00', value: 120, category: '流出' },

  { hour: '15:00', value: 150, category: '流入' },
  { hour: '15:00', value: 100, category: '流出' },

  { hour: '16:00', value: 130, category: '流入' },
  { hour: '16:00', value: 80, category: '流出' },

  { hour: '17:00', value: 110, category: '流入' },
  { hour: '17:00', value: 70, category: '流出' },

  { hour: '18:00', value: 90, category: '流入' },
  { hour: '18:00', value: 5, category: '流出' },

  { hour: '19:00', value: 70, category: '流入' },
  { hour: '19:00', value: 40, category: '流出' },

  { hour: '20:00', value: 60, category: '流入' },
  { hour: '20:00', value: 30, category: '流出' },

  { hour: '21:00', value: 50, category: '流入' },
  { hour: '21:00', value: 25, category: '流出' },

  { hour: '22:00', value: 40, category: '流入' },
  { hour: '22:00', value: 20, category: '流出' },

  { hour: '23:00', value: 30, category: '流入' },
  { hour: '23:00', value: 18, category: '流出' },
];
