export default [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: '登录',
        path: '/user/login',
        component: './User/Login',
      },
    ],
  },
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: '修改密码',
        path: '/user/change-password',
        component: './User/ChangePassword',
      },
    ],
  },
  {
    path: '/monitor',
    name: '监控中心',
    icon: 'fund',
    routes: [
      {
        path: '/monitor',
        redirect: '/monitor/statistical-view',
      },
      {
        path: '/monitor/statistical-view',
        name: '全景视图',
        icon: 'fund',
        component: './StatisticalView',
      },
      {
        path: '/monitor/statistical-view/full',
        name: '全景视图',
        icon: 'fund',
        component: './StatisticalView',
        hideInMenu: true,
        target: '_blank',
        menuRender: false,
        menuHeaderRender: false,
        headerRender: false,
        footerRender: false,
      },
      {
        path: '/monitor/statistical-view/setting',
        name: '设置',
        component: './StatisticalView/Setting',
        hideInMenu: true,
        access: 'isAdmin',
      },
      {
        path: '/monitor/dashboard',
        name: '统计视图',
        icon: 'dashboard',
        component: './Dashboard',
      },
      {
        path: '/monitor/statistical-view/monitor/:id',
        name: '监控视图',
        component: './AnalysisPage',
        hideInMenu: true,
      },
      {
        path: '/monitor/host',
        name: '监控对象',
        component: './HostMonitor',
      },
      {
        path: '/monitor/host/details/:id',
        name: '监控对象详情',
        component: './HostMonitor/HostMonitorDetails',
        hideInMenu: true,
      },
      {
        path: '/monitor/list',
        name: '监控数据',
        component: './MonitorList',
      },
      {
        path: '/monitor/list/trend-chart/:id',
        name: '趋势图',
        component: './TrendChart',
        hideInMenu: true,
      },
      {
        path: '/monitor/topology',
        name: '监控拓扑',
        icon: 'highlight',
        component: './MonitorTopology',
      },
      {
        path: '/monitor/topology/edit/:id',
        name: '编辑',
        component: './editor/Topology',
        hideInMenu: true,
      },
      {
        path: '/monitor/topology/edit/full/:id',
        name: '编辑',
        component: './editor/Topology',
        hideInMenu: true,
        menuRender: false,
        menuHeaderRender: false,
        headerRender: false,
        // footerRender: false,
      },
      {
        path: '/monitor/topology/details/:id',
        name: '详情',
        component: './editor/Topology',
        hideInMenu: true,
      },
      {
        path: '/monitor/topology/details/full/:id',
        name: '详情',
        component: './editor/Topology',
        hideInMenu: true,
        menuRender: false,
        menuHeaderRender: false,
        headerRender: false,
        // footerRender: false,
      },
      {
        path: '/monitor/performance',
        name: '性能信息',
        component: './Performance',
      },
      {
        path: '/monitor/performance/:id',
        component: './Performance/components/ThirdPartyPageEmbed',
        name: '详情',
        hideInMenu: true,
      },
      {
        path: '/monitor/performance/full/:id',
        component: './Performance/components/ThirdPartyPageEmbed',
        name: '详情',
        hideInMenu: true,
        target: '_blank',
        menuRender: false,
        menuHeaderRender: false,
        headerRender: false,
        footerRender: false,
      },
      {
        path: '/monitor/action-log',
        name: '动作日志',
        component: './ActionLog',
        hideInMenu: true,
      },
    ],
  },
  {
    path: 'alarm',
    name: '告警中心',
    icon: 'alert',
    routes: [
      {
        path: '/alarm',
        redirect: '/alarm/event',
      },
      {
        path: '/alarm/event',
        name: '告警事件',
        component: './Alarm',
      },
      {
        path: '/alarm/trigger-action',
        name: '告警通知策略',
        component: './TriggerAction',
      },
      {
        path: '/alarm/trigger-action/add',
        name: '新建告警通知策略',
        component: './TriggerAction/components/TriggerActionDetails',
        hideInMenu: true,
      },
      {
        path: '/alarm/trigger-action/edit/:id',
        name: '编辑告警通知策略',
        component: './TriggerAction/components/TriggerActionDetails',
        hideInMenu: true,
      },
    ],
  },
  {
    path: '/monitor-config',
    name: '监控配置',
    icon: 'monitor',
    routes: [
      {
        path: '/monitor-config',
        redirect: '/monitor-config/host',
      },
      {
        path: '/monitor-config/host-group',
        name: '对象群组',
        component: './HostGroup',
      },

      {
        path: '/monitor-config/host',
        name: '监控对象管理',
        component: './Host',
      },
      {
        path: '/monitor-config/host/add/:hostTypeId',
        name: '新建监控对象',
        component: './Host/components/HostDetails1',
        hideInMenu: true,
      },
      {
        path: '/monitor-config/host/edit/:id',
        name: '编辑监控对象',
        component: './Host/components/HostDetails1',
        hideInMenu: true,
      },
      {
        path: '/monitor-config/host/details/:id',
        name: '监控对象详情',
        component: './Host/HostDetails',
        hideInMenu: true,
      },
      // {
      //   path: '/monitor-config/add',
      //   name: '新建监控对象',
      //   component: './Host/components/HostDetails',
      //   hideInMenu: true,
      // },

      {
        path: '/monitor-config/host/monitor-item/:hostId',
        name: '指标配置',
        component: './MonitorItem',
        hideInMenu: true,
      },

      {
        path: '/monitor-config/host/monitor-item/:hostId/view/:id',
        name: '查看指标配置',
        component: './Host/components/MetricsConfig/ViewMetricConfig',
        hideInMenu: true,
      },
      {
        path: '/monitor-config/host/monitor-item/add',
        name: '新建监控项',
        component: './MonitorItem/components/ItemDetails',
        hideInMenu: true,
      },
      {
        path: '/monitor-config/host/monitor-item/edit/:id',
        name: '编辑监控项',
        component: './MonitorItem/components/ItemDetails',
        hideInMenu: true,
      },

      {
        path: '/monitor-config/host/trigger/:hostId',
        name: '阈值设置',
        component: './Trigger',
        hideInMenu: true,
      },
      {
        path: '/monitor-config/host/monitor-item/:hostId/setting/:itemId',
        name: '阈值设置',
        component: './Trigger',
        hideInMenu: true,
      },
      // {
      //   path: '/monitor-config/host/trigger/add',
      //   name: '新建触发器',
      //   component: './Trigger/components/TriggerForm',
      //   hideInMenu: true,
      // },

      {
        path: '/monitor-config/host/trigger/:hostId/edit/:id',
        name: '编辑阈值',
        component: './Trigger/components/TriggerForm',
        hideInMenu: true,
      },
      {
        path: '/monitor-config/host/trigger/details/:id',
        name: '查看阈值',
        component: './Trigger/components/Details',
        hideInMenu: true,
      },
      {
        path: '/monitor-config/host/trigger/details/:id/:dependenciesId',
        name: '依赖项',
        component: './Trigger/components/Details',
        hideInMenu: true,
      },

      {
        path: '/monitor-config/agent',
        name: '监控代理管理',
        component: './deployment/Host',
      },
      {
        path: '/monitor-config/template',
        name: '模板管理',
        component: './Template',
      },
      {
        path: '/monitor-config/template/edit/:id',
        name: '编辑模版',
        component: './Template/components/TemplateDetails',
        hideInMenu: true,
      },
      {
        path: '/monitor-config/template/add',
        name: '新建模版',
        component: './Template/components/TemplateDetails',
        hideInMenu: true,
      },
    ],
  },
  {
    path: '/deployment',
    name: '部署管理',
    icon: 'deploymentUnit',
    access: 'isSuperAdmin',

    routes: [
      {
        path: '/deployment',
        redirect: '/deployment/host',
      },

      {
        path: '/deployment/host-group',
        name: '主机组管理',
        component: './deployment/HostGroup',
      },
      {
        path: '/deployment/host-group/add',
        name: '新建主机组',
        component: './deployment/HostGroup/components/HostGroupDetail',
        hideInMenu: true,
      },
      {
        path: '/deployment/host-group/edit/:id',
        name: '编辑主机组',
        component: './deployment/HostGroup/components/HostGroupDetail',
        hideInMenu: true,
      },
      {
        path: '/deployment/playbook',
        name: '原子任务管理',
        component: './deployment/Playbook',
      },
      {
        path: '/deployment/playbook/upload-list',
        name: '解压列表',
        component: './deployment/UploadList',
        hideInMenu: true,
      },
      {
        path: '/deployment/jobs-flow',
        name: '监控模块管理',
        component: './deployment/JobFlow',
      },
      {
        path: '/deployment/jobs-flow/add',
        name: '新建任务流',
        component: './deployment/JobFlow/components/JobFlowDetail',
        hideInMenu: true,
      },
      {
        path: '/deployment/jobs-flow/edit/:id',
        name: '编辑任务流',
        component: './deployment/JobFlow/components/JobFlowDetail',
        hideInMenu: true,
      },
      {
        path: '/deployment/rapid-deployment',
        name: '快速部署',
        component: './deployment/RapidDeployment',
      },
      {
        path: '/deployment/rapid-deployment/add',
        name: '新建部署规则',
        component: './deployment/RapidDeployment/components/DeploymentRules',
        hideInMenu: true,
      },
      {
        path: '/deployment/rapid-deployment/edit/:id',
        name: '编辑部署规则',
        component: './deployment/RapidDeployment/components/DeploymentRules',
        hideInMenu: true,
      },
      {
        path: '/deployment/rapid-deployment/execute-details/:id',
        name: '执行详情',
        component: './deployment/RapidDeployment/components/ExecuteDetails',
        hideInMenu: true,
      },
      {
        path: '/deployment/key-management',
        name: '密钥管理',
        component: './deployment/KeyManagement',
      },
    ],
  },
  {
    path: '/system',
    name: '系统管理',
    icon: 'setting',

    access: 'isSuperAdmin',
    routes: [
      {
        path: '/system/users',
        redirect: '/system/users/user-groups',
      },
      {
        path: '/system/users/user-groups',
        name: '用户组',
        component: './UserGroups',
      },
      {
        path: '/system/users/user-groups/edit/:id',
        name: '编辑用户组',
        component: './UserGroups/components/GroupsDetails',
        hideInMenu: true,
      },
      {
        path: '/system/users/user-groups/add',
        name: '新建用户组',
        component: './UserGroups/components/GroupsDetails',
        hideInMenu: true,
      },
      {
        path: '/system/users/list',
        name: '用户',
        component: './Users',
      },
      {
        path: '/system/users/list/edit/:id',
        name: '编辑用户',
        component: './Users/<USER>/UserDetails',
        hideInMenu: true,
      },
      {
        path: '/system/users/list/add',
        name: '新建用户',
        component: './Users/<USER>/UserDetails',
        hideInMenu: true,
      },
    ],
  },

  {
    path: '/data-cockpit',
    name: '大屏demo',
    component: './DataCockpit',
    target: '_blank',
    layout: false,
  },
  {
    path: '/demo',
    name: '大屏demo',
    component: './Demo',
    target: '_blank',
    layout: false,
  },

  {
    path: '/',
    redirect: '/monitor',
  },
  {
    path: '*',
    layout: false,
    component: './404',
  },
];
